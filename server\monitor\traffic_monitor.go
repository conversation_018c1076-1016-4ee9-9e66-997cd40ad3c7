package monitor

import (
	"io"
	"log"
	"net"
	"strconv"
	"sync"
	"time"

	"socks/server/monitor/application/service"
	"socks/server/monitor/domain/entity"
)

var (
	globalMonitor *TrafficMonitor
	monitorOnce   sync.Once
)

// TrafficMonitor 流量监控器
type TrafficMonitor struct {
	appService service.MonitorAppService
	mutex      sync.RWMutex
	// 用于批量记录的缓存
	httpRequestCache map[string]int64           // key: clientUUID:serverRoute, value: requestCount
	tcpTrafficCache  map[string]*TCPTrafficData // key: clientUUID:serverPort
	cacheFlushTicker *time.Ticker
}

// TCPTrafficData TCP流量数据
type TCPTrafficData struct {
	BytesIn  int64
	BytesOut int64
}

// ClientInfo 客户端信息（为了向后兼容保留）
type ClientInfo = entity.ClientInfo

// GetGlobalMonitor 获取全局流量监控器实例
func GetGlobalMonitor() *TrafficMonitor {
	monitorOnce.Do(func() {
		globalMonitor = &TrafficMonitor{
			appService:       service.GetMonitorAppService(),
			httpRequestCache: make(map[string]int64),
			tcpTrafficCache:  make(map[string]*TCPTrafficData),
		}

		// 启动缓存刷新任务（每分钟刷新一次）
		globalMonitor.startCacheFlushTask()
	})
	return globalMonitor
}

// CreateHTTPMonitor 创建HTTP监控记录
func (tm *TrafficMonitor) CreateHTTPMonitor(client ClientInfo, serverRoute, serviceName string) error {
	clientInfo := &entity.ClientInfo{
		UUID:  client.UUID,
		IP:    client.IP,
		Name:  client.Name,
		Group: client.Group,
	}
	return tm.appService.CreateHTTPMonitor(clientInfo, serverRoute, serviceName)
}

// CreateTCPMonitor 创建TCP监控记录
func (tm *TrafficMonitor) CreateTCPMonitor(client ClientInfo, serverPort int, serviceName string) error {
	clientInfo := &entity.ClientInfo{
		UUID:  client.UUID,
		IP:    client.IP,
		Name:  client.Name,
		Group: client.Group,
	}
	return tm.appService.CreateTCPMonitor(clientInfo, serverPort, serviceName)
}

// RecordHTTPRequest 记录HTTP请求（缓存模式）
func (tm *TrafficMonitor) RecordHTTPRequest(clientUUID, serverRoute string) {
	tm.mutex.Lock()
	defer tm.mutex.Unlock()

	key := clientUUID + ":" + serverRoute
	tm.httpRequestCache[key]++
}

// RecordHTTPRequestDirect 直接记录HTTP请求（不使用缓存）
func (tm *TrafficMonitor) RecordHTTPRequestDirect(clientUUID, serverRoute string, requestCount int64) error {
	return tm.appService.RecordHTTPRequest(clientUUID, serverRoute, requestCount)
}

// RecordTCPTraffic 记录TCP流量（缓存模式）
func (tm *TrafficMonitor) RecordTCPTraffic(clientUUID string, serverPort int, bytesIn, bytesOut int64) {
	tm.mutex.Lock()
	defer tm.mutex.Unlock()

	key := clientUUID + ":" + strconv.Itoa(serverPort)
	if data, exists := tm.tcpTrafficCache[key]; exists {
		data.BytesIn += bytesIn
		data.BytesOut += bytesOut
	} else {
		tm.tcpTrafficCache[key] = &TCPTrafficData{
			BytesIn:  bytesIn,
			BytesOut: bytesOut,
		}
	}
}

// RecordTCPTrafficDirect 直接记录TCP流量（不使用缓存）
func (tm *TrafficMonitor) RecordTCPTrafficDirect(clientUUID string, serverPort int, bytesIn, bytesOut int64) error {
	return tm.appService.RecordTCPTraffic(clientUUID, serverPort, bytesIn, bytesOut)
}

// DeleteMonitorsByClient 删除指定客户端的所有监控记录
func (tm *TrafficMonitor) DeleteMonitorsByClient(clientUUID string) error {
	return tm.appService.DeleteMonitorsByClient(clientUUID)
}

// startCacheFlushTask 启动缓存刷新任务
func (tm *TrafficMonitor) startCacheFlushTask() {
	tm.cacheFlushTicker = time.NewTicker(1 * time.Minute)

	go func() {
		for range tm.cacheFlushTicker.C {
			tm.flushCache()
		}
	}()
}

// flushCache 刷新缓存到数据库
func (tm *TrafficMonitor) flushCache() {
	tm.mutex.Lock()
	defer tm.mutex.Unlock()

	// 刷新HTTP请求缓存
	for key, count := range tm.httpRequestCache {
		if count > 0 {
			// 解析key
			parts := parseKey(key)
			if len(parts) == 2 {
				clientUUID, serverRoute := parts[0], parts[1]
				if err := tm.RecordHTTPRequestDirect(clientUUID, serverRoute, count); err != nil {
					log.Printf("Failed to flush HTTP request cache for %s: %v", key, err)
				}
			}
		}
	}

	// 刷新TCP流量缓存
	for key, data := range tm.tcpTrafficCache {
		if data.BytesIn > 0 || data.BytesOut > 0 {
			// 解析key
			parts := parseKey(key)
			if len(parts) == 2 {
				clientUUID := parts[0]
				serverPort, err := strconv.Atoi(parts[1])
				if err == nil && serverPort > 0 {
					if err := tm.RecordTCPTrafficDirect(clientUUID, serverPort, data.BytesIn, data.BytesOut); err != nil {
						log.Printf("Failed to flush TCP traffic cache for %s: %v", key, err)
					}
				}
			}
		}
	}

	// 清空缓存
	tm.httpRequestCache = make(map[string]int64)
	tm.tcpTrafficCache = make(map[string]*TCPTrafficData)
}

// parseKey 解析缓存key
func parseKey(key string) []string {
	result := make([]string, 0, 2)
	lastIndex := 0

	for i, char := range key {
		if char == ':' {
			result = append(result, key[lastIndex:i])
			lastIndex = i + 1
		}
	}

	if lastIndex < len(key) {
		result = append(result, key[lastIndex:])
	}

	return result
}

// MonitoredConn 带流量监控的连接包装器
type MonitoredConn struct {
	net.Conn
	clientUUID string
	serverPort int
	monitor    *TrafficMonitor
	bytesRead  int64
	bytesWrite int64
}

// NewMonitoredConn 创建带监控的连接
func (tm *TrafficMonitor) NewMonitoredConn(conn net.Conn, clientUUID string, serverPort int) *MonitoredConn {
	return &MonitoredConn{
		Conn:       conn,
		clientUUID: clientUUID,
		serverPort: serverPort,
		monitor:    tm,
	}
}

// Read 读取数据并记录流量
func (mc *MonitoredConn) Read(b []byte) (n int, err error) {
	n, err = mc.Conn.Read(b)
	if n > 0 {
		mc.bytesRead += int64(n)
		mc.monitor.RecordTCPTraffic(mc.clientUUID, mc.serverPort, int64(n), 0)
	}
	return
}

// Write 写入数据并记录流量
func (mc *MonitoredConn) Write(b []byte) (n int, err error) {
	n, err = mc.Conn.Write(b)
	if n > 0 {
		mc.bytesWrite += int64(n)
		mc.monitor.RecordTCPTraffic(mc.clientUUID, mc.serverPort, 0, int64(n))
	}
	return
}

// Close 关闭连接并记录最终流量
func (mc *MonitoredConn) Close() error {
	// 记录最终的流量数据
	if mc.bytesRead > 0 || mc.bytesWrite > 0 {
		if err := mc.monitor.RecordTCPTrafficDirect(mc.clientUUID, mc.serverPort, mc.bytesRead, mc.bytesWrite); err != nil {
			log.Printf("Failed to record final traffic data: %v", err)
		}
	}
	return mc.Conn.Close()
}

// MonitoredReader 带流量监控的Reader
type MonitoredReader struct {
	io.Reader
	clientUUID  string
	serverRoute string
	monitor     *TrafficMonitor
}

// NewMonitoredReader 创建带监控的Reader
func (tm *TrafficMonitor) NewMonitoredReader(reader io.Reader, clientUUID, serverRoute string) *MonitoredReader {
	return &MonitoredReader{
		Reader:      reader,
		clientUUID:  clientUUID,
		serverRoute: serverRoute,
		monitor:     tm,
	}
}

// Read 读取数据并记录HTTP请求
func (mr *MonitoredReader) Read(p []byte) (n int, err error) {
	n, err = mr.Reader.Read(p)
	if n > 0 {
		// 对于HTTP请求，我们在每次读取时记录一次请求
		mr.monitor.RecordHTTPRequest(mr.clientUUID, mr.serverRoute)
	}
	return
}

// Stop 停止流量监控
func (tm *TrafficMonitor) Stop() {
	if tm.cacheFlushTicker != nil {
		tm.cacheFlushTicker.Stop()
		// 最后刷新一次缓存
		tm.flushCache()
	}
}
