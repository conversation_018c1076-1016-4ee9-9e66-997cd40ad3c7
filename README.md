I'll first locate where the server's HTTP URL endpoints are defined in this repo so I can enumerate them for the design doc. After that initial discovery, I'll set up a small task list to structure the work.

## 概述

本文档是 socks 项目的软件设计文档（不包含 TestServer 工程）。目标是：

- 列出服务端提供的 URL 接口与功能点
- 系统架构与组件关系（服务端、客户端、隧道/连接管理）
- 服务端与客户端通信协议设计（控制面与数据面）
- URL 路径代理与端口代理的工作流、复用、流式传输等行为
- 连接生命周期、缓存与监控、容错与重连策略
- 启动与初始化、持久化与回收

文中接口与行为基于当前代码与既定需求（用户记忆中的偏好与约束）。TestServer 仅用于本地测试，不纳入本文档范围。

---

## 系统架构

- 服务端（Server）
  - HTTP 管理面（ManagerPort）：提供注册、分配、URL 映射管理接口
  - TCP 代理入口（URLProxyPort）：直接监听 TCP 端口，解析 HTTP/HTTPS 流并转发
  - 隧道与连接管理：维护客户端控制连接（SafeConn）与 URLProxyTunnel
  - 流量监控子系统：按客户端与 BaseURL 维度统计请求量
  - 持久化与缓存：启动时从 DB 恢复映射；定时清理过期缓存

- 客户端（Client）
  - 向服务端注册（控制连接）：/register 建立长连接（HTTP Hijack → TCP）
  - URL 映射管理：/url/register、/url/unregister
  - 数据通道建立：/tcp/data 按 conn_id 建立数据长连接
  - 本地转发：面向目标服务创建本地 TCP 连接，转发数据与结果
  - 重连：仅在读失败（如 ReadJSON）时触发；阻塞式重连，每 30 秒重试

- 调用方（外部请求者）
  - 通过服务端 TCP 监听端口发起 HTTP/HTTPS 请求
  - 多路复用与长连接：同一 TCP 连接上多次 HTTP 请求（keep-alive）

---

## URL 接口与功能点

以下为服务端 main.go 注册的接口（方法、路径、用途、关键参数）：

1) GET /register
- 用途：客户端注册控制连接，升级为 TCP 长连接（控制隧道 SafeConn）
- 参数：name, type, group, id（clientUUID）
- 行为：HTTP Hijack → 返回 101 Switching Protocols（Upgrade: tcp），记录客户端信息、建立控制通道；初始化/恢复客户端相关状态

2) GET /allocate
- 用途：为客户端分配公网端口做端口代理
- 参数：id（clientUUID）, port（客户端本地 ip:port 或端口）, service_name
- 行为：在服务端开放一个公网端口，监听进入连接并转发至该客户端指定服务；返回分配的服务端端口号

3) POST /tunnel/refresh
- 用途：隧道/连接的保活与状态刷新（心跳/续期）
- 参数：通常包含 clientUUID、隧道标识、时间戳等（以实际实现为准）
- 行为：刷新服务端对该隧道的活跃时间与状态，避免被过期清理

4) GET /clients/filter
- 用途：按条件筛选客户端
- 参数：筛选条件（如组、类型、名称等，以实现为准）
- 行为：返回符合条件的客户端列表（JSON）

5) GET /clients/groups
- 用途：按组聚合返回客户端信息
- 参数：无
- 行为：返回分组后的客户端列表（JSON）

6) POST /url/register
- 用途：注册 URL 路径代理映射
- Body：URLRegisterRequest（JSON）
  - ClientUUID, AppName, BaseURL, ServiceName, ServiceGroup, ApiType, ServicePort
- 行为：创建 BaseURL → 客户端本地服务端口的映射，可能返回代理路径（形如 /api/{client}/...）；初始化 HTTP 流量监控项；映射在线

7) POST /url/unregister
- 用途：注销 URL 路径代理映射
- Body：包含 clientUUID、要注销的 urlPath 和 baseURL（以实际 DTO 为准）
- 行为：移除映射、监控项与缓存；映射离线

8) GET /tcp/data
- 用途：客户端为某 conn_id 建立数据长连接（数据通道）
- 参数：client_uuid, conn_id
- 行为：HTTP Hijack → 返回 101 Switching Protocols（Upgrade: tcp）；随后服务端将该数据通道与来自调用方的请求连接绑定，实现请求/响应的字节流转发

---

## 核心功能点详述

### 1. 控制连接与隧道（SafeConn）

- 建立：
  - 客户端 GET /register 携带 id（clientUUID）等信息
  - 服务端 Hijack HTTP 连接，返回 101，升级为 TCP 长连接
  - 服务端为该 clientUUID 维护一个 SafeConn（控制通道），集中承载：
    - 控制类 JSON 消息（如 tcp_open、tcp_close、错误通知）
    - URL 代理请求元数据（如 URLProxyMessage）或触发数据面连接建立的信令

- 复用与生命周期：
  - 控制连接为长连接，允许承载多次请求/响应协商
  - 仅当客户端明确发送“关闭”消息或读取失败（EOF）时关闭连接
  - 关闭前需读取至 io.EOF 以确保对端发送完毕（避免半关闭导致数据丢失）
  - 服务端断开后，客户端应进入阻塞式重连（每 30 秒重试，成功后重新注册并替换全局连接管理器 globalCM）

- 消息结构（控制面）：
  - 通用格式：ConnMessage
    - ID：关联的数据连接或请求标识（如 conn_id）
    - Type：消息类型（tcp_open、tcp_data、tcp_close、proxy_request、proxy_response 等）
    - Data：二进制/JSON 数据（视 Type 而定；若为二进制，建议 Base64 编码以入 JSON）

- 隧道管理：
  - URLProxyService 维护 tunnels: map[clientUUID]*URLProxyTunnel
  - 常用方法：
    - AddTunnel/GetTunnel/DeleteTunnel
    - SendProxyRequest：向指定客户端发送代理请求，返回一个 respChan 用于接收客户端响应
  - 清理：
    - 隧道退出时应从字典中移除，避免内存泄漏
    - 定时清理不活跃映射与响应通道

### 2. 数据通道与 TCP 转发

- 数据通道建立：
  - 服务端处理来自调用方的 TCP 连接，解析为一个或多个 HTTP 请求（keep-alive）
  - 服务端对每个请求分配一个 conn_id，并通过 SafeConn 下发“tcp_open”至客户端，附带 appName|baseURL 等信息
  - 客户端据此发起 GET /tcp/data?client_uuid=...&conn_id=...，服务端 Hijack 成为数据 TCP 连接
  - 服务端将“调用方连接”和“客户端数据连接”进行绑定，形成一对端到端的 TCP 转发通道

- HTTP 请求改写（URL 代理场景）：
  - 服务端在解析出请求后，查找 BaseURL 映射，得到目标 ip:port 及目标内部路径
  - 修改请求对象：
    - RequestURI、URL.Path → 目标内部路径（targetPath）
    - Host → 对应 ip:port（由映射提供）
    - 清理 URL.Scheme 与 URL.Host，让 DumpRequest 基于 RequestURI/Host 重建请求行
  - DumpRequest 含请求体（支持大包与流式）
  - 将改写后的完整 HTTP 请求字节发送至客户端

- 流式传输与大包支持：
  - 采用 TCP 流直转发而非 HTTP 消息再构造，减少内存拷贝和限制
  - 客户端将收到的请求字节全量写入本地目标 TCP 连接（不改内容，仅在必要时补充 Host: localhost:port）
  - 响应以字节流回传服务端，服务端将其直透回调用方连接
  - 支持同一连接上多次请求，客户端需检测请求完成边界（Content-Length 或 chunked）并发送 close 信令

- 连接复用与关闭：
  - 调用方连接可复用多个 HTTP 请求；数据连接与之对应，一个 conn_id 一个数据连接
  - 仅在客户端或调用方明确完成并发送关闭（EOF）时，服务端才关闭对应连接
  - 客户端在检测到 HTTP 请求完成时，向服务端发送 close（或直接半关闭），服务端再映射到调用方连接的关闭

### 3. URL 路径代理

- 映射注册：
  - POST /url/register
  - 一个 Client 可为多个 BaseURL 注册映射；每个 BaseURL 对应一个 ServicePort 与 ServiceName、ServiceGroup
  - 注册后创建监控项（client、baseURL、serviceName 维度）

- 查找与路由：
  - 服务端收到调用方请求后，按请求路径匹配 BaseURL；支持通配符前缀（/api/xxx/**）
  - 如果映射在线且目标客户端控制隧道可用，则进入转发路径，否则返回 503/404

- 映射注销：
  - POST /url/unregister
  - 清除 URL → 端口映射、监控项、缓存与响应通道

### 4. 端口代理（直连端口 → 客户端本地服务）

- 分配与监听：
  - GET /allocate 为 clientUUID 的某个本地端口（或 ip:port）在服务端分配一个公网端口
  - 服务端在该公网端口上监听 TCP，接收外部请求并转发到客户端本地服务
  - 同步保留关联关系（clientUUID、service_name、ip:port）

- 缓存与过期：
  - 端口映射具备缓存与过期管理策略：当超过 cacheExpiration 未访问且客户端未重新请求，则不复用
  - 定时清理不活跃映射，清理时同步 DB

### 5. 直接 TCP 端口监听（HTTP/HTTPS 原始解析）

- 服务端在 URLProxyPort 上直接监听 TCP
- 进入连接后使用 http.ReadRequest 解析请求，并支持 keep-alive 多次请求
- 将请求改写后通过数据通道直连客户端本地服务
- 响应以字节流返回（无需 HTTP 框架）

### 6. 流量监控

- 注册客户端与 BaseURL 映射时创建监控项
- 每次 HTTP 请求记录：RecordHTTPRequest(clientUUID, baseURL)
- 使用 monitor.GetGlobalMonitor() 获取全局统计对象

---

## 通信协议设计

### 1) 控制面协议（SafeConn）

- 建立：HTTP → 101 Switching Protocols（Upgrade: tcp），形成 TCP 长连接
- 消息封装：JSON 序列化的 ConnMessage
  - 字段：
    - id：string，标识关联的请求/数据连接（conn_id）
    - type：string，消息类型
    - data：[]byte | JSON 序列化字节。若为二进制建议 Base64 封装
- 典型消息类型：
  - tcp_open：服务端 → 客户端，通知新请求到来，data 可携带 "appName|baseURL" 等元信息
  - tcp_data：服务端 ↔ 客户端，承载小量/控制类数据（大流量数据通过 /tcp/data 的 TCP 通道承载，避免控制面拥塞）
  - tcp_close：任意方向，指示该 conn_id 的数据流关闭
  - proxy_request/proxy_response：URL 代理路径下的信令封装（如 URLProxyMessage）

- 可靠性与顺序：
  - TCP 长连接保证有序传输
  - 控制面应尽可能小数据量，承载会话管理、边界通知

- 异常处理与重连：
  - 当服务端 Read 失败（如 EOF）或 Write 失败：关闭连接并清理隧道
  - 客户端仅在读取失败（如 ReadJSON）时触发阻塞式重连（每 30 秒重试），重连成功后重新 /register 并替换旧隧道

### 2) 数据面协议（/tcp/data）

- 建立：GET /tcp/data?client_uuid=...&conn_id=... → 101 Switching Protocols（Upgrade: tcp）
- 绑定关系：服务端将该数据 TCP 连接与调用方连接绑定（以 conn_id 关联）
- 传输内容：原始 TCP 字节流（含完整 HTTP 请求及响应）
- 边界与关闭：
  - 客户端根据 HTTP 语义检测请求结束（Content-Length、chunked、连接关闭）
  - 请求完成后，客户端应向服务端发送 close（或半关闭），服务端再对调用方连接执行对应关闭
  - 若对端明确发送 close 消息，则本端需要继续读取直到 io.EOF 再关闭 socket，避免数据截断

### 3) URL 映射数据结构（抽象）

- 映射键：baseURL（如 /api/xxx）
- 值：服务信息
  - serviceName、serviceGroup、servicePort（或 ip:port）
  - online 标志
  - client 关联（clientUUID、name、group、ip）
- 连接结构（Connection）应统一承载：
  - 控制面 SafeConn 引用
  - 客户端属性（之前 ProxyService/URLProxyService 分散字段整合到 Connection）
  - URL 映射集合（urlMappings）由 Connection 管理统一访问

---

## 服务端模块设计

- main
  - 读取配置（util.SystemConfig）
  - 启动监控系统 monitor.StartMonitoring()
  - 启动 TCP 代理监听（URLProxyPort）
  - 注册 HTTP 管理接口并监听（ManagerPort）
  - 请求日志中间件：RequestLogger

- handlers
  - PortProxyHandler
    - AllocateHandler：公网端口分配与监听
    - RegisterHandler：控制连接注册（Hijack → TCP）
    - RefreshHandler：心跳/续期
    - FilterClientsHandler/GetClientsByGroupHandler：客户端查询
    - init()：RecoverFromDB，启动加载 DB 记录构建内存状态
  - URLProxyHandler
    - RegisterURLHandler/UnregisterURLHandler：映射管理
  - TCPProxyHandler
    - DataTCPHandler：数据通道建立（Hijack → TCP）
    - StartTCPProxyServer(port)：直接 TCP 监听，解析 HTTP 请求
    - handleTCPConnection：一个 TCP 连接上循环解析多个请求
    - handleSingleRequest：改写请求/记录监控/发起数据面转发
    - handleTCPForwardSingle：与客户端控制/数据通道交互，完成单次请求转发
    - sendErrorResponse：错误返回（如 404/503/500）

- application/domain
  - event.URLProxyTunnel：控制响应通道管理（SendProxyRequest、DeleteRespChan 等）
  - service.URLProxyService/PortProxyService：业务逻辑、映射与隧道字典
  - entity：ConnMessage、URLProxyMessage、URLMapping、Client、SafeConn 等实体
  - monitor：流量统计
  - util：配置与工具

---

## 客户端模块设计（行为视角）

- 注册与重连
  - 启动后发起 GET /register?name=&type=&group=&id=
  - 成功后建立 SafeConn，进入读循环：处理服务端控制消息
  - 当 ReadJSON 失败触发阻塞式重连（每 30s，成功后替换 globalCM）

- URL 映射管理
  - POST /url/register 注册需要暴露的 BaseURL 到本地服务端口的映射（支持分组与命名）
  - 可按需 /url/unregister 注销

- 数据通道工作流
  - 接收到 tcp_open(id, appName|baseURL) → 建立 GET /tcp/data?client_uuid=&conn_id=
  - 建立本地到目标服务（localhost:port）的 TCP 连接
  - 将服务端传来的请求字节流写入本地连接（必要时添加 Host: localhost:port）
  - 将本地服务响应的字节流回写到数据通道
  - 请求完成后发送关闭通知/半关闭，等待服务端读取至 EOF 再完成关闭

- 端口代理
  - 按需调用 /allocate 获取公网映射端口
  - 服务端来连接后，客户端同样执行本地连接与转发

- 关闭策略
  - 不主动关闭控制与数据连接，除非收到明确关闭信令或读失败
  - 在 handleTCPConnection 外的专用 goroutine 监听来自服务端的“客户端断开”通知，避免在单请求处理函数中阻塞

---

## 生命周期与资源管理

- 启动/恢复
  - 服务端启动时：从 gateway_intranet_tunnel 表读取持久化记录
    - 填充 clientPortCache
    - 初始化自增 ID 起始值
    - 恢复 URL 映射与端口监听
- 过期与清理
  - 定时器清理不活跃的端口映射与 URL 映射
  - 当 tunnels 中的连接退出时，立即从字典移除，避免泄漏
- 复用与关闭
  - 调用方连接允许 keep-alive 多请求；数据通道按 conn_id 一一对应
  - 关闭时遵循“先读尽再关”的策略，确保跨端无丢包

---

## 错误处理与可观测性

- 请求日志：时间戳、方法、URI、远端地址
- 错误响应：
  - 404 Not Found：未匹配到 BaseURL 映射
  - 503 Service Unavailable：映射离线或客户端隧道不可用
  - 500 Internal Server Error：序列化/转发等内部错误
- 监控：按 clientUUID + baseURL 维度统计 HTTP 请求量，支持后续可视化

---

## 安全与配置

- 访问控制
  - 建议对 /register、/url/register、/allocate 等敏感接口加鉴权（Token/签名/双向 TLS）
- 传输安全
  - 控制面与数据面目前采用明文 TCP（Hijack）；部署时建议加隧道层加密或在 LB 层启用 TLS
- 配置
  - 通过 util.SystemConfig 注入 ManagerPort、URLProxyPort 等，建议支持环境变量与配置文件

---

## 关键设计决策与用户偏好落实

- URL 代理采用“TCP 直转发”而非服务端构造 HTTP 请求，支持大包与流式
- 将 URL 路径包含在 TCP 请求中下发给客户端，客户端负责补齐 Host 与本地转发
- 连接复用：TCP 连接承载多个 HTTP 请求，客户端检测请求完成后发送 close
- 仅在读取失败时触发重连；重连函数为阻塞式，成功后再返回
- 客户端在收到服务端 close 时，不立即关闭通道，而是等待本地服务响应完成
- handleTCPConnection 内部专注按请求处理；客户端断开监控应在独立 goroutine 中进行
- 统一 Connection 结构收敛分散在 ProxyService/URLProxyService 的 client 字段；URL 映射集合由 Connection 统一管理
- 隧道退出必须从字典移除，避免内存泄漏与 GC 阻塞

---

## 典型时序（简述）

- 控制连接建立
  - Client → Server: GET /register?id=UUID → 101 Upgrade
  - 双方：建立 SafeConn，Server 记录 tunnels[UUID] = SafeConn

- URL 映射注册
  - Client → Server: POST /url/register {ClientUUID, BaseURL, ServicePort, ...}
  - Server：建立/更新映射，创建监控项

- 调用方请求路径代理
  - Caller → Server(URLProxyPort TCP)：发送 HTTP 请求
  - Server：解析请求 → 匹配 BaseURL → 改写 Host/Path → 生成 conn_id
  - Server → Client：SafeConn 发送 tcp_open(conn_id, appName|baseURL)
  - Client → Server：GET /tcp/data?client_uuid=&conn_id= → 101 Upgrade
  - Server ↔ Client：经数据通道转发请求/响应字节流
  - Client：检测请求完成 → 发送关闭通知 → Server：读尽后关闭，回收资源

---

## 后续工作建议

- 抽象并文档化 ConnMessage 与 URLProxyMessage 的字段与编码方案（如 Data 是否固定使用 Base64）
- 为 /tunnel/refresh、/clients/filter、/url/unregister 明确定义完整 DTO
- 实施鉴权与 TLS，以满足生产安全要求
- 编写端到端自动化测试与压力测试，验证 keep-alive、重连、流式传输、错误注入等场景
- 完成 Connection 结构的统一改造与 URL 映射迁移
- 加入端口映射的过期与 DB 清理的定时器任务

---

如果你希望，我可以将本设计文档导出为 Markdown 文件放入仓库 docs/ 目录，或者补充时序图/组件图来辅助沟通。需要的话请告知文件名与放置路径。
