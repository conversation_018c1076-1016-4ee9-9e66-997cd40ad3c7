package monitor

import (
	"log"
	"net"
)

// 以下是集成示例代码，展示如何在现有的处理器中使用流量监控

// HTTPProxyIntegrationExample HTTP代理集成示例
func HTTPProxyIntegrationExample() {
	monitor := GetGlobalMonitor()

	// 示例：创建HTTP监控记录
	client := ClientInfo{
		UUID:  "client-uuid-123",
		Name:  "example-client",
		IP:    "*************",
		Group: "production",
	}

	err := monitor.CreateHTTPMonitor(client, "/api/v1", "api-service")
	if err != nil {
		log.Printf("Failed to create HTTP monitor: %v", err)
		return
	}

	// 示例：记录HTTP请求
	monitor.RecordHTTPRequest("client-uuid-123", "/api/v1")

	log.Printf("HTTP monitoring example completed")
}

// TCPProxyIntegrationExample TCP代理集成示例
func TCPProxyIntegrationExample() {
	monitor := GetGlobalMonitor()

	// 示例：创建TCP监控记录
	client := ClientInfo{
		UUID:  "client-uuid-456",
		Name:  "example-client",
		IP:    "*************",
		Group: "production",
	}

	err := monitor.CreateTCPMonitor(client, 9080, "database-service")
	if err != nil {
		log.Printf("Failed to create TCP monitor: %v", err)
		return
	}

	// 示例：记录TCP流量
	monitor.RecordTCPTraffic("client-uuid-456", 9080, 1024, 512) // 1KB入站，512B出站

	log.Printf("TCP monitoring example completed")
}

// MonitoredConnectionExample 监控连接示例
func MonitoredConnectionExample() {
	monitor := GetGlobalMonitor()

	// 模拟一个TCP连接
	// 在实际使用中，这应该是真实的网络连接
	var conn net.Conn // 这里应该是实际的连接

	// 包装连接以自动监控流量
	monitoredConn := monitor.NewMonitoredConn(conn, "client-uuid-789", 8080)

	// 使用monitoredConn进行读写操作，流量会自动记录
	// 例如：
	// buffer := make([]byte, 1024)
	// n, err := monitoredConn.Read(buffer)
	// if err == nil {
	//     // 流量已自动记录
	// }

	// 关闭连接时会记录最终的流量数据
	// monitoredConn.Close()

	_ = monitoredConn // 避免未使用变量警告

	log.Printf("Monitored connection example completed")
}

// ClientDisconnectionExample 客户端断开连接示例
func ClientDisconnectionExample(clientUUID string) {
	monitor := GetGlobalMonitor()

	// 清理客户端的所有监控记录
	err := monitor.DeleteMonitorsByClient(clientUUID)
	if err != nil {
		log.Printf("Failed to cleanup monitors for client %s: %v", clientUUID, err)
	} else {
		log.Printf("Successfully cleaned up monitors for client %s", clientUUID)
	}
}

// 以下是在现有代码中集成流量监控的示例

// IntegrateWithTCPProxyHandler 在TCP代理处理器中集成流量监控
func IntegrateWithTCPProxyHandler() {
	// 这是一个示例，展示如何在现有的TCP代理处理器中集成流量监控

	// 1. 获取全局监控器
	monitor := GetGlobalMonitor()

	// 2. 在处理连接时记录HTTP请求
	// 在 handleTCPConnection 方法中添加：
	// monitor.RecordHTTPRequest(mapping.Client.UUID, baseURL)

	// 3. 包装连接以自动监控流量
	// 在数据传输逻辑中：
	// monitoredConn := monitor.NewMonitoredConn(clientConn, mapping.Client.UUID, mapping.ServerPort)
	// 然后使用 monitoredConn 替代原始连接进行数据传输

	_ = monitor // 避免未使用变量警告
	log.Printf("TCP proxy handler integration example")
}

// IntegrateWithPortProxyTunnel 在端口代理隧道中集成流量监控
func IntegrateWithPortProxyTunnel() {
	// 这是一个示例，展示如何在端口代理隧道中集成流量监控

	// 1. 获取全局监控器
	monitor := GetGlobalMonitor()

	// 2. 在创建端口映射时初始化TCP监控
	// 在 AllocateAvailablePort 方法中添加：
	// client := ClientInfo{
	//     UUID:  clientUUID,
	//     IP:    client.IP,
	//     Name:  client.Name,
	//     Group: client.Group,
	// }
	// monitor.CreateTCPMonitor(client, serverPort, serviceName)

	// 3. 在数据传输时包装连接
	// 在 StartTransData2RequestClient 方法中：
	// monitoredConn := monitor.NewMonitoredConn(requestConn, p.clientId, p.serverPort)

	_ = monitor // 避免未使用变量警告
	log.Printf("Port proxy tunnel integration example")
}

// IntegrateWithURLProxyService 在URL代理服务中集成流量监控
func IntegrateWithURLProxyService() {
	// 这是一个示例，展示如何在URL代理服务中集成流量监控

	// 1. 获取全局监控器
	monitor := GetGlobalMonitor()

	// 2. 在注册URL映射时初始化HTTP监控
	// 在 RegisterURLMapping 方法中添加：
	// client := ClientInfo{
	//     UUID:  clientUUID,
	//     IP:    client.IP,
	//     Name:  client.Name,
	//     Group: client.Group,
	// }
	// for baseURL, serviceInfo := range mapping.BaseURL {
	//     monitor.CreateHTTPMonitor(client, baseURL, serviceInfo.ServiceName)
	// }

	// 3. 在处理HTTP请求时记录流量
	// 在请求处理逻辑中：
	// monitor.RecordHTTPRequest(clientUUID, baseURL)

	_ = monitor // 避免未使用变量警告
	log.Printf("URL proxy service integration example")
}

// IntegrateWithClientDisconnection 在客户端断开连接时集成清理逻辑
func IntegrateWithClientDisconnection() {
	// 这是一个示例，展示如何在客户端断开连接时清理监控记录

	// 1. 获取全局监控器
	monitor := GetGlobalMonitor()

	// 2. 在客户端断开连接的处理逻辑中添加清理代码
	// 在 UnregisterClient 或类似方法中：
	// err := monitor.DeleteMonitorsByClient(clientUUID)
	// if err != nil {
	//     log.Printf("Failed to cleanup monitors for client %s: %v", clientUUID, err)
	// }

	_ = monitor // 避免未使用变量警告
	log.Printf("Client disconnection integration example")
}

// StartMonitoring 启动流量监控系统
func StartMonitoring() {
	// 获取全局监控器，这会自动启动缓存刷新任务
	monitor := GetGlobalMonitor()

	log.Printf("Traffic monitoring system started")

	// 监控器会自动：
	// 1. 每分钟刷新缓存到数据库
	// 2. 清理超过24小时的旧数据
	// 3. 记录HTTP请求次数和TCP流量数据

	_ = monitor // 避免未使用变量警告
}

// RunAllExamples 运行所有示例
func RunAllExamples() {
	log.Printf("Running all traffic monitoring examples...")

	HTTPProxyIntegrationExample()
	TCPProxyIntegrationExample()
	MonitoredConnectionExample()
	ClientDisconnectionExample("example-client-uuid")

	log.Printf("All examples completed")
}

// StopMonitoring 停止流量监控系统
func StopMonitoring() {
	monitor := GetGlobalMonitor()
	monitor.Stop()

	log.Printf("Traffic monitoring system stopped")
}
