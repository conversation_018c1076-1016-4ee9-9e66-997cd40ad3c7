package util

import (
	"fmt"
	"net"

	"github.com/google/uuid"
)

// GenerateCilentUUID 生成客户端UUID
func GenerateCilentUUID() (string, error) {
	macAddr, err := getMACAddress()
	if err != nil {
		return "", err
	}

	ipAddr, err := getIPAddress()
	if err != nil {
		return "", err
	}

	// 使用 MAC 地址和 IP 地址的哈希生成 UUID
	namespaceUUID := uuid.NewMD5(uuid.Nil, []byte(macAddr+ipAddr))

	return namespaceUUID.String(), nil
}

func getMACAddress() (string, error) {
	interfaces, err := net.Interfaces()
	if err != nil {
		return "", err
	}

	for _, inter := range interfaces {
		if len(inter.HardwareAddr) > 0 {
			return inter.HardwareAddr.String(), nil
		}
	}
	return "", fmt.Errorf("no MAC address found")
}

func getIPAddress() (string, error) {
	conn, err := net.Dial("udp", "*******:80")
	if err != nil {
		return "", err
	}
	defer conn.Close()

	localAddr := conn.LocalAddr().(*net.UDPAddr)
	return localAddr.IP.String(), nil
}
