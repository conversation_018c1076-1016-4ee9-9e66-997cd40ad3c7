# Socks 项目软件设计文档

## 概述

本文档是 socks 项目的软件设计文档。该项目是一个基于 TCP 长连接的内网穿透代理系统，支持端口代理和 URL 路径代理两种模式。

### 项目目标
- 提供稳定的内网穿透服务
- 支持端口代理和 URL 路径代理
- 实现高性能的 TCP 数据转发
- 提供完整的连接管理和监控功能
- 支持客户端自动重连和故障恢复

## 系统架构

### 整体架构
```
外部请求者 ←→ 服务端 ←→ 客户端 ←→ 本地服务
           (公网)    (内网穿透)   (内网)
```

### 核心组件

#### 服务端（Server）
- **HTTP 管理面（ManagerPort）**：提供客户端注册、端口分配、URL 映射管理等接口
- **TCP 代理入口（URLProxyPort）**：直接监听 TCP 端口，解析 HTTP/HTTPS 请求并转发
- **隧道与连接管理**：维护客户端控制连接（SafeConn）与数据通道
- **流量监控子系统**：按客户端与 BaseURL 维度统计请求量和流量
- **持久化与缓存**：启动时从数据库恢复映射；定时清理过期缓存

#### 客户端（Client）
- **控制连接管理**：向服务端注册并维护长连接
- **URL 映射管理**：注册和管理本地服务的 URL 映射
- **数据通道建立**：按需建立数据传输通道
- **本地转发**：连接本地服务并转发数据
- **自动重连**：检测连接断开并自动重连

#### 外部请求者
- 通过服务端 TCP 监听端口发起 HTTP/HTTPS 请求
- 支持 HTTP keep-alive 长连接复用

## 功能模块详述

### 1. 客户端注册与连接管理

**接口**：`GET /register`
- **功能**：客户端注册控制连接，建立 TCP 长连接
- **参数**：
  - `name`：客户端名称
  - `type`：客户端类型
  - `group`：客户端组别
  - `id`：客户端 UUID
  - `ip`：客户端 IP 地址
- **流程**：
  1. HTTP 连接升级为 TCP 长连接（101 Switching Protocols）
  2. 服务端记录客户端信息并建立控制通道
  3. 初始化客户端相关状态和监控项

### 2. 端口代理分配

**接口**：`GET /allocate`
- **功能**：为客户端分配公网端口进行端口代理
- **参数**：
  - `id`：客户端 UUID
  - `port`：客户端本地服务端口（ip:port 格式）
  - `service_name`：服务名称
- **流程**：
  1. 在服务端分配一个可用的公网端口
  2. 启动端口监听器
  3. 建立端口映射关系并持久化
  4. 返回分配的服务端端口号

### 3. URL 路径代理注册

**接口**：`POST /url/register`
- **功能**：注册 URL 路径到本地服务的映射
- **参数**：
  - `api_type`：API 类型（Agent、AI、API 等）
  - `service_group`：服务组别
  - `service_name`：服务名称
  - `service_port`：本地服务端口
  - `app_name`：应用名称
  - `client_id`：客户端 UUID
  - `base_url`：基础 URL 路径
- **流程**：
  1. 验证客户端是否已注册
  2. 创建 URL 映射关系
  3. 初始化流量监控
  4. 返回代理路径信息

### 4. URL 路径代理注销

**接口**：`POST /url/unregister`
- **功能**：注销已注册的 URL 路径映射
- **流程**：
  1. 移除 URL 映射关系
  2. 清理相关监控项和缓存
  3. 更新映射状态为离线

### 5. 数据通道建立

**接口**：`GET /tcp/data`
- **功能**：客户端为特定连接建立数据传输通道
- **参数**：
  - `client_uuid`：客户端 UUID
  - `conn_id`：连接 ID
  - `ip`：客户端 IP
- **流程**：
  1. HTTP 连接升级为 TCP 长连接
  2. 将数据通道与请求连接绑定
  3. 实现双向字节流转发

### 6. 隧道状态刷新

**接口**：`POST /tunnel/refresh`
- **功能**：隧道连接的保活与状态刷新
- **流程**：
  1. 刷新隧道的活跃时间
  2. 更新连接状态
  3. 防止连接被过期清理

### 7. 客户端查询

**接口**：
- `GET /clients/filter`：按条件筛选客户端
- `GET /clients/groups`：按组聚合返回客户端信息

**功能**：提供客户端信息查询和管理功能

## 通信协议设计

### 控制面协议（SafeConn）

#### 连接建立
- **方式**：HTTP → 101 Switching Protocols（Upgrade: tcp）
- **特性**：TCP 长连接，保证有序传输

#### 消息格式
```json
{
  "id": "连接标识符",
  "type": "消息类型",
  "data": "二进制数据（Base64编码）"
}
```

#### 消息类型
- **tcp_open**：服务端→客户端，通知新请求到来
- **tcp_data**：双向，承载控制类数据
- **tcp_close**：双向，指示连接关闭
- **proxy_request**：服务端→客户端，URL 代理请求
- **proxy_response**：客户端→服务端，URL 代理响应

#### URL 代理消息格式
```json
{
  "id": "连接ID",
  "type": "消息类型",
  "data": "消息数据",
  "base_url": "基础URL路径",
  "target_url": "目标URL",
  "method": "HTTP方法",
  "headers": "HTTP头部",
  "body": "请求/响应体",
  "status": "HTTP状态码",
  "is_stream": "是否流式传输",
  "stream_type": "流类型",
  "chunk_index": "数据块索引"
}
```

### 数据面协议（/tcp/data）

#### 连接建立
- **方式**：GET /tcp/data?client_uuid=...&conn_id=... → 101 Switching Protocols
- **绑定**：服务端将数据连接与请求连接绑定

#### 传输内容
- **格式**：原始 TCP 字节流
- **内容**：完整的 HTTP 请求和响应数据

#### 连接关闭
- 客户端检测 HTTP 请求结束标志
- 发送关闭通知或半关闭
- 读取至 io.EOF 后完全关闭连接

### 重连机制

#### 触发条件
- 客户端读取操作失败（如 ReadJSON 错误）
- 不在启动监控时触发

#### 重连策略
- **方式**：阻塞式重连
- **间隔**：每 30 秒重试一次
- **流程**：
  1. 重新调用 /register 接口
  2. 建立新的控制连接
  3. 替换全局连接管理器
  4. 恢复所有映射关系

## 服务端模块设计

### 主程序模块（main）
- 读取系统配置
- 启动流量监控系统
- 启动 TCP 代理监听器
- 注册 HTTP 管理接口
- 提供请求日志中间件

### 处理器模块（handlers）

#### PortProxyHandler
- **AllocateHandler**：端口分配与监听
- **RegisterHandler**：客户端注册和连接升级
- **RefreshHandler**：隧道状态刷新
- **FilterClientsHandler**：客户端筛选查询
- **GetClientsByGroupHandler**：分组客户端查询
- **init()**：从数据库恢复端口映射

#### URLProxyHandler
- **RegisterURLHandler**：URL 映射注册
- **UnregisterURLHandler**：URL 映射注销

#### TCPProxyHandler
- **DataTCPHandler**：数据通道建立
- **StartTCPProxyServer**：TCP 代理服务器启动
- **handleTCPConnection**：TCP 连接处理
- **handleSingleRequest**：单个 HTTP 请求处理
- **handleTCPForwardSingle**：TCP 转发处理
- **sendErrorResponse**：错误响应发送

### 应用服务层（application）

#### 领域服务
- **URLProxyService**：URL 代理业务逻辑
- **PortProxyService**：端口代理业务逻辑

#### 事件处理
- **URLProxyTunnel**：URL 代理隧道管理
- **PortProxyTunnel**：端口代理隧道管理
- **Connection**：统一连接管理

### 领域模型层（domain）

#### 实体对象
- **Client**：客户端信息
- **SafeConn**：安全连接封装
- **ConnMessage**：控制消息
- **URLProxyMessage**：URL 代理消息
- **PortMapping**：端口映射
- **URLMapping**：URL 映射

#### 仓储接口
- **PortMappingDao**：端口映射数据访问
- **MonitorDao**：监控数据访问

### 基础设施层（infra）
- 数据库访问实现
- 配置管理
- 工具类

## 客户端模块设计

### 核心组件

#### 连接管理
- **SafeConn**：TCP 连接封装，支持 JSON 消息收发
- **ConnectionManager**：本地连接管理器
- **重连机制**：自动检测和恢复连接

#### 配置管理
- **TunnelClientConfig**：客户端配置结构
- **Tunnel**：隧道配置定义
- **配置加载**：支持文件和命令行参数

#### API 服务器
- **端口映射管理**：`POST /port/register`
- **URL 映射管理**：`POST /url/register`
- **状态查询**：`GET /status`

#### 消息处理
- **open**：处理端口代理连接请求
- **tcp_open**：处理 URL 代理连接请求
- **data**：处理数据传输
- **close**：处理连接关闭

### 工作流程

#### 启动流程
1. 解析配置参数
2. 注册到服务端
3. 建立全局连接管理器
4. 加载隧道配置
5. 启动 API 服务器

#### 数据转发流程
1. 接收服务端请求通知
2. 建立数据通道连接
3. 连接本地目标服务
4. 双向数据转发
5. 检测请求结束并关闭连接

## 流量监控系统

### 监控维度
- **客户端维度**：按 UUID 统计
- **服务维度**：按 BaseURL 统计
- **时间维度**：支持小时级汇总

### 监控指标
- **HTTP 请求数**：请求次数统计
- **TCP 流量**：字节数统计（上行/下行）
- **连接状态**：在线/离线状态

### 数据存储
- **实时缓存**：内存中临时存储
- **持久化**：定期刷新到数据库
- **清理机制**：定时清理过期数据

## 数据持久化

### 数据库表结构

#### 端口映射表（gateway_intranet_tunnel）
- 存储端口映射关系
- 支持映射状态管理
- 提供过期时间控制

#### 监控数据表
- 存储流量统计信息
- 支持历史数据查询
- 提供数据聚合功能

### 缓存管理
- **启动恢复**：从数据库加载映射关系
- **实时更新**：映射变更时同步更新
- **过期清理**：定时清理不活跃映射

## 错误处理与容错

### 连接异常处理
- **读取失败**：触发重连机制
- **写入失败**：关闭连接并清理资源
- **超时处理**：设置合理的超时时间

### 服务异常处理
- **404 错误**：映射不存在
- **503 错误**：服务不可用
- **500 错误**：内部服务器错误

### 资源清理
- **连接清理**：及时关闭无效连接
- **内存清理**：清理过期的响应通道
- **隧道清理**：从字典中移除退出的隧道

## 性能优化

### 网络优化
- **TCP 参数调优**：禁用 Nagle 算法，启用 keep-alive
- **缓冲区优化**：增大读写缓冲区大小
- **连接复用**：支持 HTTP keep-alive

### 并发处理
- **协程池**：合理控制协程数量
- **锁优化**：减少锁竞争，使用读写锁
- **异步处理**：非阻塞的消息处理

### 内存管理
- **对象池**：复用频繁创建的对象
- **及时清理**：避免内存泄漏
- **监控告警**：监控内存使用情况

## 安全考虑

### 访问控制
- **客户端认证**：基于 UUID 的客户端识别
- **权限管理**：按组别进行权限控制

### 数据安全
- **传输加密**：支持 HTTPS 和 TLS
- **数据完整性**：确保数据传输完整

### 防护措施
- **连接限制**：限制单客户端连接数
- **超时控制**：防止连接长时间占用
- **异常监控**：及时发现异常行为

## 部署与运维

### 配置管理
- **服务端配置**：端口范围、超时时间、数据库连接等
- **客户端配置**：服务器地址、隧道配置等

### 监控告警
- **连接监控**：监控连接状态和数量
- **性能监控**：监控响应时间和吞吐量
- **错误监控**：监控错误率和异常情况

### 日志管理
- **请求日志**：记录所有 HTTP 请求
- **错误日志**：记录系统错误和异常
- **性能日志**：记录性能相关指标

## 后续优化建议

### 功能增强
- **负载均衡**：支持多客户端负载均衡
- **健康检查**：定期检查服务健康状态
- **配置热更新**：支持配置动态更新

### 性能提升
- **协议优化**：考虑使用更高效的二进制协议
- **压缩传输**：支持数据压缩减少带宽占用
- **连接池**：实现连接池提高复用率

### 运维改进
- **可视化界面**：提供 Web 管理界面
- **API 接口**：提供完整的管理 API
- **自动化部署**：支持容器化部署
