package service

import (
	"fmt"
	"log"
	"sync"
	"time"

	"socks/server/monitor/domain/entity"
	"socks/server/monitor/domain/repo"
)

var (
	monitorService *MonitorService
	msOnce         sync.Once
)

// MonitorService 监控领域服务
type MonitorService struct {
	repository repo.MonitorRepository
	mutex      sync.RWMutex
}

// GetMonitorService 获取监控服务实例
func GetMonitorService(repository repo.MonitorRepository) *MonitorService {
	msOnce.Do(func() {
		monitorService = &MonitorService{
			repository: repository,
		}
	})
	return monitorService
}

// CreateHTTPMonitor 创建HTTP监控记录
func (ms *MonitorService) CreateHTTPMonitor(client *entity.ClientInfo, serverRoute, serviceName string) error {
	ms.mutex.Lock()
	defer ms.mutex.Unlock()

	// 检查是否已存在
	existing, err := ms.repository.GetByHTTPRoute(client.UUID, serverRoute)
	if err == nil && existing != nil {
		log.Printf("HTTP monitor already exists for client %s, route %s", client.UUID, serverRoute)
		return nil
	}

	monitor := entity.NewHTTPMonitor(client, serverRoute, serviceName)
	err = ms.repository.Create(monitor)
	if err != nil {
		return fmt.Errorf("failed to create HTTP monitor: %v", err)
	}

	log.Printf("Created HTTP monitor for client %s, route %s", client.UUID, serverRoute)
	return nil
}

// CreateTCPMonitor 创建TCP监控记录
func (ms *MonitorService) CreateTCPMonitor(client *entity.ClientInfo, serverPort int, serviceName string) error {
	ms.mutex.Lock()
	defer ms.mutex.Unlock()

	// 检查是否已存在
	existing, err := ms.repository.GetByTCPPort(client.UUID, serverPort)
	if err == nil && existing != nil {
		log.Printf("TCP monitor already exists for client %s, port %d", client.UUID, serverPort)
		return nil
	}

	monitor := entity.NewTCPMonitor(client, serverPort, serviceName)
	err = ms.repository.Create(monitor)
	if err != nil {
		return fmt.Errorf("failed to create TCP monitor: %v", err)
	}

	log.Printf("Created TCP monitor for client %s, port %d", client.UUID, serverPort)
	return nil
}

// RecordHTTPRequest 记录HTTP请求
func (ms *MonitorService) RecordHTTPRequest(clientUUID, serverRoute string, requestCount int64) error {
	if requestCount <= 0 {
		return nil
	}

	trafficData := entity.NewHTTPTrafficData(requestCount)
	err := ms.repository.AddTrafficData(clientUUID, serverRoute, 0, entity.TunnelTypeHTTP, trafficData)
	if err != nil {
		log.Printf("Failed to record HTTP request for client %s, route %s: %v", clientUUID, serverRoute, err)
		return err
	}

	return nil
}

// RecordTCPTraffic 记录TCP流量
func (ms *MonitorService) RecordTCPTraffic(clientUUID string, serverPort int, bytesIn, bytesOut int64) error {
	if bytesIn <= 0 && bytesOut <= 0 {
		return nil
	}

	trafficData := entity.NewTCPTrafficData(bytesIn, bytesOut)
	err := ms.repository.AddTrafficData(clientUUID, "", serverPort, entity.TunnelTypeTCP, trafficData)
	if err != nil {
		log.Printf("Failed to record TCP traffic for client %s, port %d: %v", clientUUID, serverPort, err)
		return err
	}

	return nil
}

// GetMonitorByHTTPRoute 根据HTTP路由获取监控记录
func (ms *MonitorService) GetMonitorByHTTPRoute(clientUUID, serverRoute string) (*entity.TunnelMonitor, error) {
	return ms.repository.GetByHTTPRoute(clientUUID, serverRoute)
}

// GetMonitorByTCPPort 根据TCP端口获取监控记录
func (ms *MonitorService) GetMonitorByTCPPort(clientUUID string, serverPort int) (*entity.TunnelMonitor, error) {
	return ms.repository.GetByTCPPort(clientUUID, serverPort)
}

// GetMonitorsByClient 根据客户端UUID获取所有监控记录
func (ms *MonitorService) GetMonitorsByClient(clientUUID string) ([]*entity.TunnelMonitor, error) {
	return ms.repository.GetByClientUUID(clientUUID)
}

// GetMonitorsByGroup 根据客户端组获取所有监控记录
func (ms *MonitorService) GetMonitorsByGroup(clientGroup string) ([]*entity.TunnelMonitor, error) {
	return ms.repository.GetByClientGroup(clientGroup)
}

// GetTrafficDataInRange 获取指定时间范围内的流量数据
func (ms *MonitorService) GetTrafficDataInRange(clientUUID string, start, end time.Time) ([]*entity.TunnelMonitor, error) {
	return ms.repository.GetTrafficDataInRange(clientUUID, start, end)
}

// GetActiveMonitors 获取活跃的监控记录
func (ms *MonitorService) GetActiveMonitors(since time.Time) ([]*entity.TunnelMonitor, error) {
	return ms.repository.GetActiveMonitors(since)
}

// CleanOldData 清理旧数据
func (ms *MonitorService) CleanOldData() error {
	// 清理超过25小时的数据（保留24小时数据的缓冲）
	olderThan := time.Now().Add(-25 * time.Hour)
	return ms.repository.CleanOldData(olderThan)
}

// DeleteMonitorsByClient 删除指定客户端的所有监控记录
func (ms *MonitorService) DeleteMonitorsByClient(clientUUID string) error {
	ms.mutex.Lock()
	defer ms.mutex.Unlock()

	err := ms.repository.DeleteByClientUUID(clientUUID)
	if err != nil {
		return fmt.Errorf("failed to delete monitors for client %s: %v", clientUUID, err)
	}

	log.Printf("Deleted all monitors for client %s", clientUUID)
	return nil
}

// GetAllMonitors 获取所有监控记录
func (ms *MonitorService) GetAllMonitors() ([]*entity.TunnelMonitor, error) {
	return ms.repository.GetAll()
}

// GetTrafficSummary 获取流量汇总信息
func (ms *MonitorService) GetTrafficSummary(clientUUID string, hours int) (*entity.TrafficSummary, error) {
	start := time.Now().Add(-time.Duration(hours) * time.Hour)
	end := time.Now()

	monitors, err := ms.GetTrafficDataInRange(clientUUID, start, end)
	if err != nil {
		return nil, err
	}

	summary := entity.NewTrafficSummary(clientUUID, hours)
	for _, monitor := range monitors {
		summary.AddMonitorData(monitor)
	}

	return summary, nil
}

// StartCleanupTask 启动定期清理任务
func (ms *MonitorService) StartCleanupTask() {
	go func() {
		ticker := time.NewTicker(1 * time.Hour) // 每小时清理一次
		defer ticker.Stop()

		for range ticker.C {
			if err := ms.CleanOldData(); err != nil {
				log.Printf("Failed to clean old monitor data: %v", err)
			} else {
				log.Printf("Successfully cleaned old monitor data")
			}
		}
	}()
}
