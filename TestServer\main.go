package main

import (
	"crypto/rand"
	"crypto/rsa"
	"crypto/tls"
	"crypto/x509"
	"crypto/x509/pkix"
	"encoding/pem"
	"flag"
	"fmt"
	"log"
	"math/big"
	"net"
	"net/http"
	"time"
)

// 生成临时证书
func generateTempCert() (tls.Certificate, error) {
	// 生成私钥
	privateKey, err := rsa.GenerateKey(rand.Reader, 2048)
	if err != nil {
		return tls.Certificate{}, err
	}

	// 创建证书模板
	template := x509.Certificate{
		SerialNumber: big.NewInt(1),
		Subject: pkix.Name{
			Organization: []string{"Test Server"},
		},
		NotBefore: time.Now(),
		NotAfter:  time.Now().Add(time.Hour * 24 * 180), // 180天有效期

		KeyUsage:              x509.KeyUsageKeyEncipherment | x509.KeyUsageDigitalSignature,
		ExtKeyUsage:           []x509.ExtKeyUsage{x509.ExtKeyUsageServerAuth},
		BasicConstraintsValid: true,
		DNSNames:              []string{"localhost"},
		IPAddresses:           []net.IP{net.ParseIP("127.0.0.1")},
	}

	// 创建证书
	derBytes, err := x509.CreateCertificate(rand.Reader, &template, &template, &privateKey.PublicKey, privateKey)
	if err != nil {
		return tls.Certificate{}, err
	}

	// 编码证书和私钥
	certPEM := pem.EncodeToMemory(&pem.Block{Type: "CERTIFICATE", Bytes: derBytes})
	privateKeyPEM := pem.EncodeToMemory(&pem.Block{Type: "RSA PRIVATE KEY", Bytes: x509.MarshalPKCS1PrivateKey(privateKey)})

	// 加载证书
	cert, err := tls.X509KeyPair(certPEM, privateKeyPEM)
	if err != nil {
		return tls.Certificate{}, err
	}

	return cert, nil
}

func main() {
	httpPort := flag.Int("http", 8000, "HTTP服务监听端口")
	httpsPort := flag.Int("https", 8443, "HTTPS服务监听端口")
	flag.Parse()

	// 注册路由处理器
	mux := http.NewServeMux()
	mux.HandleFunc("/", handleRoot)
	mux.HandleFunc("/echo", handleEcho)
	mux.HandleFunc("/time", handleTime)
	mux.HandleFunc("/headers", handleHeaders)

	// 生成临时证书
	cert, err := generateTempCert()
	if err != nil {
		log.Fatal("生成证书失败:", err)
	}

	// 创建TLS配置
	tlsConfig := &tls.Config{
		Certificates: []tls.Certificate{cert},
	}

	// HTTP服务器
	httpServer := &http.Server{
		Addr:    fmt.Sprintf("0.0.0.0:%d", *httpPort),
		Handler: mux,
	}

	// HTTPS服务器
	httpsServer := &http.Server{
		Addr:      fmt.Sprintf("0.0.0.0:%d", *httpsPort),
		Handler:   mux,
		TLSConfig: tlsConfig,
	}

	// 启动HTTP服务器
	go func() {
		log.Printf("HTTP服务器启动在端口 %d", *httpPort)
		if err := httpServer.ListenAndServe(); err != nil {
			log.Printf("HTTP服务器错误: %v", err)
		}
	}()

	// 启动HTTPS服务器
	log.Printf("HTTPS服务器启动在端口 %d (自签名证书)", *httpsPort)
	if err := httpsServer.ListenAndServeTLS("", ""); err != nil {
		log.Printf("HTTPS服务器错误: %v", err)
	}
}

// 根路径处理器
func handleRoot(w http.ResponseWriter, r *http.Request) {
	fmt.Fprintf(w, "测试服务器运行正常! (%s)\n", r.Proto)
	fmt.Fprintf(w, "可用的测试接口:\n")
	fmt.Fprintf(w, "- GET /echo?message=xxx  : 回显消息\n")
	fmt.Fprintf(w, "- GET /time            : 返回服务器时间\n")
	fmt.Fprintf(w, "- GET /headers         : 显示请求头信息\n")
}

// 回显处理器
func handleEcho(w http.ResponseWriter, r *http.Request) {
	message := r.URL.Query().Get("message")
	if message == "" {
		message = "没有提供消息"
	}
	fmt.Fprintf(w, "收到消息: %s\n", message)
}

// 时间处理器
func handleTime(w http.ResponseWriter, r *http.Request) {
	now := time.Now().Format("2006-01-02 15:04:05")
	fmt.Fprintf(w, "服务器时间: %s\n", now)
}

// 请求头处理器
func handleHeaders(w http.ResponseWriter, r *http.Request) {
	fmt.Fprintln(w, "收到的HTTP头:")
	for name, values := range r.Header {
		for _, value := range values {
			fmt.Fprintf(w, "%s: %s\n", name, value)
		}
	}
}
