package service

import (
	"log"
	"sync"
	"time"

	"socks/server/domain/event"
	"socks/server/util"
)

var (
	cacheCleaner *CacheCleaner
	ccOnce       sync.Once
)

// CacheCleaner 缓存清理器
type CacheCleaner struct {
	connection *event.Connection
	expiration time.Duration
	ticker     *time.Ticker
	stopChan   chan bool
	running    bool
	mutex      sync.RWMutex
}

// GetCacheCleaner 获取缓存清理器实例
func GetCacheCleaner(config *util.TunnelConfig) *CacheCleaner {
	ccOnce.Do(func() {
		cacheCleaner = &CacheCleaner{
			connection: event.GetConnection(config),
			expiration: time.Duration(config.SlidingExpiration*24) * time.Hour,
			stopChan:   make(chan bool, 1),
		}
	})
	return cacheCleaner
}

// Start 启动缓存清理器
func (cc *CacheCleaner) Start() {
	cc.mutex.Lock()
	defer cc.mutex.Unlock()

	if cc.running {
		return
	}

	// 设置定时器，每隔过期时间的一半进行一次清理检查
	cleanInterval := cc.expiration / 2
	if cleanInterval < time.Minute {
		cleanInterval = time.Minute // 最小清理间隔为1分钟
	}

	cc.ticker = time.NewTicker(cleanInterval)
	cc.running = true

	log.Printf("start cache cleaner, clean interval: %v, expiration: %v", cleanInterval, cc.expiration)

	go cc.cleanLoop()
}

// Stop 停止缓存清理器
func (cc *CacheCleaner) Stop() {
	cc.mutex.Lock()
	defer cc.mutex.Unlock()

	if !cc.running {
		return
	}

	cc.running = false
	if cc.ticker != nil {
		cc.ticker.Stop()
	}

	select {
	case cc.stopChan <- true:
	default:
	}

	log.Printf("cache cleaner stopped")
}

// cleanLoop 清理循环
func (cc *CacheCleaner) cleanLoop() {
	defer func() {
		if r := recover(); r != nil {
			log.Printf("cache cleaner panic: %v", r)
		}
	}()

	for {
		select {
		case <-cc.ticker.C:
			cc.performCleanup()
		case <-cc.stopChan:
			log.Printf("receive stop signal, exit clean loop")
			return
		}
	}
}

// performCleanup 执行清理操作
func (cc *CacheCleaner) performCleanup() {
	log.Printf("start clean cache, expiration threshold: %v", cc.expiration)

	startTime := time.Now()
	err := cc.connection.CleanExpiredMappings(cc.expiration)
	duration := time.Since(startTime)

	if err != nil {
		log.Printf("clean cache fail: %v, duration: %v", err, duration)
	}
}

// IsRunning 检查清理器是否正在运行
func (cc *CacheCleaner) IsRunning() bool {
	cc.mutex.RLock()
	defer cc.mutex.RUnlock()
	return cc.running
}

// UpdateExpiration 更新过期时间
func (cc *CacheCleaner) UpdateExpiration(expiration time.Duration) {
	cc.mutex.Lock()
	defer cc.mutex.Unlock()

	cc.expiration = expiration
	log.Printf("update cache expiration to: %v", expiration)

	// 如果正在运行，重启以应用新的过期时间
	if cc.running {
		cc.Stop()
		cc.Start()
	}
}
