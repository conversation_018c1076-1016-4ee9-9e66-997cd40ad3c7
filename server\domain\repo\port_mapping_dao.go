package repo

import (
	"socks/server/infra/dao"
	"socks/server/infra/repo"
	"sync"
	"time"
)

var (
	portMappingDao PortMappingDao
	once           sync.Once
)

func GetPortMappingDao() PortMappingDao {
	once.Do(func() {
		portMappingDao = dao.GetIntranetTunnelDaoImpl()
	})
	return portMappingDao
}

func SetPortMappingDao(dao PortMappingDao) {
	portMappingDao = dao
}

type PortMappingDao interface {
	Create(tunnel *repo.IntranetTunnel) (int, error)
	// GetByID 根据ID获取隧道
	GetIntranetTunnelByID(id int) (*repo.IntranetTunnel, error)

	GetIntranetTunnelsByIDs(id []int) ([]*repo.IntranetTunnel, error)

	// GetAll 获取所有隧道
	GetAll() ([]*repo.IntranetTunnel, error)

	// GetEnabled 获取所有启用的隧道
	GetEnabled() ([]*repo.IntranetTunnel, error)

	// Update 更新隧道信息
	Update(tunnel *repo.IntranetTunnel) error

	// Delete 删除隧道
	Delete(id int) error

	// UpdateLastConnectionTime 更新最后连接时间
	UpdateLastConnectionTime(id int) error

	UpdateOnlineStatus(id int, online bool) error

	// GetExpiredTunnels 获取过期的隧道记录
	GetExpiredTunnels(expiration time.Duration) ([]*repo.IntranetTunnel, error)

	// BatchDelete 批量删除隧道记录
	BatchDelete(ids []int) error
}
