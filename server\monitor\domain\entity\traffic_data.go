package entity

import (
	"time"
)

// TunnelType 隧道类型枚举
type TunnelType string

const (
	TunnelTypeHTTP TunnelType = "HTTP" // HTTP请求代理
	TunnelTypeTCP  TunnelType = "TCP"  // TCP端口代理
)

// TrafficData 时序流量数据实体
type TrafficData struct {
	Timestamp    time.Time `json:"timestamp"`     // 时间戳（分钟级别）
	RequestCount int64     `json:"request_count"` // HTTP请求次数（仅HTTP类型使用）
	BytesIn      int64     `json:"bytes_in"`      // 入站流量字节数（仅TCP类型使用）
	BytesOut     int64     `json:"bytes_out"`     // 出站流量字节数（仅TCP类型使用）
}

// NewHTTPTrafficData 创建HTTP流量数据
func NewHTTPTrafficData(requestCount int64) *TrafficData {
	return &TrafficData{
		Timestamp:    time.Now().Truncate(time.Minute),
		RequestCount: requestCount,
		BytesIn:      0,
		BytesOut:     0,
	}
}

// NewTCPTrafficData 创建TCP流量数据
func NewTCPTrafficData(bytesIn, bytesOut int64) *TrafficData {
	return &TrafficData{
		Timestamp:    time.Now().Truncate(time.Minute),
		RequestCount: 0,
		BytesIn:      bytesIn,
		BytesOut:     bytesOut,
	}
}

// IsHTTPData 判断是否为HTTP数据
func (t *TrafficData) IsHTTPData() bool {
	return t.RequestCount > 0 && t.BytesIn == 0 && t.BytesOut == 0
}

// IsTCPData 判断是否为TCP数据
func (t *TrafficData) IsTCPData() bool {
	return t.RequestCount == 0 && (t.BytesIn > 0 || t.BytesOut > 0)
}

// Add 累加流量数据
func (t *TrafficData) Add(other *TrafficData) {
	if other == nil {
		return
	}
	t.RequestCount += other.RequestCount
	t.BytesIn += other.BytesIn
	t.BytesOut += other.BytesOut
}

// TrafficDataList 流量数据列表
type TrafficDataList []*TrafficData

// Add 添加流量数据到列表
func (list *TrafficDataList) Add(data *TrafficData) {
	if data == nil {
		return
	}

	// 确保时间戳为分钟级别
	data.Timestamp = data.Timestamp.Truncate(time.Minute)

	// 查找是否已存在相同时间戳的数据
	for _, existing := range *list {
		if existing.Timestamp.Equal(data.Timestamp) {
			existing.Add(data)
			return
		}
	}

	// 添加新数据点
	*list = append(*list, data)

	// 保持最近24小时的数据
	list.cleanOldData()
}

// cleanOldData 清理超过24小时的数据
func (list *TrafficDataList) cleanOldData() {
	if len(*list) == 0 {
		return
	}

	cutoffTime := time.Now().Add(-24 * time.Hour).Truncate(time.Minute)

	// 过滤掉超过24小时的数据
	var validData TrafficDataList
	for _, data := range *list {
		if data.Timestamp.After(cutoffTime) || data.Timestamp.Equal(cutoffTime) {
			validData = append(validData, data)
		}
	}

	*list = validData
}

// GetInRange 获取指定时间范围内的流量数据
func (list TrafficDataList) GetInRange(start, end time.Time) TrafficDataList {
	var result TrafficDataList

	start = start.Truncate(time.Minute)
	end = end.Truncate(time.Minute)

	for _, data := range list {
		if (data.Timestamp.After(start) || data.Timestamp.Equal(start)) &&
			(data.Timestamp.Before(end) || data.Timestamp.Equal(end)) {
			result = append(result, data)
		}
	}

	return result
}

// GetTotalTraffic 获取总流量统计
func (list TrafficDataList) GetTotalTraffic() (totalRequests int64, totalBytesIn int64, totalBytesOut int64) {
	for _, data := range list {
		totalRequests += data.RequestCount
		totalBytesIn += data.BytesIn
		totalBytesOut += data.BytesOut
	}
	return
}
