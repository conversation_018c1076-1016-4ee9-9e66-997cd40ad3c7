package repo

import (
	"time"

	"socks/server/monitor/domain/entity"
)

// MonitorRepository 监控仓储接口
type MonitorRepository interface {
	// Create 创建新的监控记录
	Create(monitor *entity.TunnelMonitor) error

	// GetByID 根据ID获取监控记录
	GetByID(id int) (*entity.TunnelMonitor, error)

	// GetByHTTPRoute 根据HTTP路由获取监控记录
	GetByHTTPRoute(clientUUID, serverRoute string) (*entity.TunnelMonitor, error)

	// GetByTCPPort 根据TCP端口获取监控记录
	GetByTCPPort(clientUUID string, serverPort int) (*entity.TunnelMonitor, error)

	// GetByClientUUID 根据客户端UUID获取所有监控记录
	GetByClientUUID(clientUUID string) ([]*entity.TunnelMonitor, error)

	// GetByClientGroup 根据客户端组获取所有监控记录
	GetByClientGroup(clientGroup string) ([]*entity.TunnelMonitor, error)

	// GetByTunnelType 根据隧道类型获取所有监控记录
	GetByTunnelType(tunnelType entity.TunnelType) ([]*entity.TunnelMonitor, error)

	// GetAll 获取所有监控记录
	GetAll() ([]*entity.TunnelMonitor, error)

	// Update 更新监控记录
	Update(monitor *entity.TunnelMonitor) error

	// UpdateTrafficData 更新流量数据
	UpdateTrafficData(id int, trafficData entity.TrafficDataList) error

	// Delete 删除监控记录
	Delete(id int) error

	// DeleteByClientUUID 删除指定客户端的所有监控记录
	DeleteByClientUUID(clientUUID string) error

	// AddTrafficData 添加流量数据点
	AddTrafficData(clientUUID, serverRoute string, serverPort int, tunnelType entity.TunnelType, data *entity.TrafficData) error

	// GetTrafficDataInRange 获取指定时间范围内的流量数据
	GetTrafficDataInRange(clientUUID string, start, end time.Time) ([]*entity.TunnelMonitor, error)

	// CleanOldData 清理超过指定时间的旧数据
	CleanOldData(olderThan time.Time) error

	// GetActiveMonitors 获取活跃的监控记录（最近有流量数据的）
	GetActiveMonitors(since time.Time) ([]*entity.TunnelMonitor, error)

	// BatchUpdateTrafficData 批量更新流量数据
	BatchUpdateTrafficData(updates []TrafficDataUpdate) error
}

// TrafficDataUpdate 流量数据更新结构
type TrafficDataUpdate struct {
	ID          int                      `json:"id"`
	TrafficData entity.TrafficDataList  `json:"traffic_data"`
}
